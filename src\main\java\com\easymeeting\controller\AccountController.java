package com.easymeeting.controller;


import com.easymeeting.entity.vo.Result;
import com.wf.captcha.ArithmeticCaptcha;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户信息表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2025-07-06
 */
@Slf4j
@RestController
@RequestMapping("/account")
public class AccountController {
    // 测试
    @RequestMapping("/test")
    public String test() {
        return "测试成功";
    }

    /**
     * 获取验证码
     *
     * @return 验证码信息
     */
    @RequestMapping("/captcha")
    public Result getCaptcha() {
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(100, 40);
        String code = captcha.text();
        String codeToBase64 = captcha.toBase64();
        log.info("验证码：{}", code);
        return Result.success(codeToBase64);
    }
}
