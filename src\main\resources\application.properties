# \u5E94\u7528\u670D\u52A1 WEB \u8BBF\u95EE\u7AEF\u53E3
server.port=6060
ws.port=6061
server.servlet.context-path=/api
#session\u8FC7\u671F\u65F6\u95F4 60M \u4E00\u4E2A\u5C0F\u65F6
server.servlet.session.timeout=PT60M
#\u5904\u7406favicon
spring.mvc.favicon.enable=false
spring.servlet.multipart.max-file-size=15MB
spring.servlet.multipart.max-request-size=15MB
#\u9519\u8BEF\u9875\u5904\u7406
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false
#\u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.url=***********************************************************************************************************************************************\
  &useSSL=false
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.pool-name=HikariCPDatasource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=180000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1
#Spring redis\u914D\u7F6E
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=0
spring.redis.host=127.0.0.1
spring.redis.port=6379
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.jedis.pool.max-active=20
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.jedis.pool.max-wait=-1
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.jedis.pool.max-idle=10
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.jedis.pool.min-idle=0
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=2000
# redis\u914D\u7F6E\u7ED3\u675F
# \u6D88\u606F\u5904\u7406 \u53EF\u9009 rabbitmq \u6216 redis
messaging.handle.channel=redis
#rabbitmq\u914D\u7F6E
rabbitmq.host=localhost
rabbitmq.port=5672
#\u9879\u76EE\u76EE\u5F55
project.folder=D:/workspace/easymeeting
#\u65E5\u5FD7\u7EA7\u522B\u914D\u7F6E
log.root.level=debug
#\u8D85\u7EA7\u7BA1\u7406\u5458id
admin.emails=<EMAIL>