<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="AdditionalModuleElements">
    <content url="file://$MODULE_DIR$" dumb="true">
      <sourceFolder url="file://$MODULE_DIR$/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
    </content>
  </component>
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5lYXN5bWVldGluZzwvaWQ+PGNsYXNzcGF0aD48ZGlyIG5hbWU9IkQ6L3dvcmtzcGFjZS9lYXN5bWVldGluZy90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
          </map>
        </option>
        <option name="version" value="5" />
      </configuration>
    </facet>
  </component>
</module>