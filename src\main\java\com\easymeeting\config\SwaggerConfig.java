package com.easymeeting.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置类
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("EasyMeeting API文档")
                        .description("EasyMeeting在线会议系统API接口文档")
                        .version("1.0")
                        .contact(new Contact()
                                .name("EasyMeeting Team")
                                .url("https://github.com/easymeeting")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache License 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")));
    }
}
