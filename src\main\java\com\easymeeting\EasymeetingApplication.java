package com.easymeeting;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication // SpringBoot启动类
@MapperScan("com.easymeeting.mappers")
@EnableTransactionManagement // 启动事务
@EnableAsync // 启动异步
@EnableScheduling // 启动定时任务
public class EasymeetingApplication {
    public static void main(String[] args) {
        SpringApplication.run(EasymeetingApplication.class, args);
    }
}
